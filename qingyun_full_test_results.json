{"claude-3-5-sonnet-latest": {"display_name": "[q<PERSON><PERSON>] claude-3.5-sonnet", "connection": true, "financial": true, "conn_time": 3.9683213233947754, "fin_time": 8.880890369415283, "overall": true}, "claude-3-7-sonnet-latest": {"display_name": "[q<PERSON><PERSON>] claude-3.7-sonnet", "connection": false, "financial": false, "conn_time": 0.0, "fin_time": 0.0, "overall": false}, "gemini-2.0-flash": {"display_name": "[qingyun] gemini-2.0-flash", "connection": true, "financial": true, "conn_time": 8.210765361785889, "fin_time": 5.584283113479614, "overall": true}, "gemini-2.5-pro-exp-03-25": {"display_name": "[q<PERSON><PERSON>] gemini-2.5-pro", "connection": true, "financial": true, "conn_time": 10.932390689849854, "fin_time": 17.315053462982178, "overall": true}, "gpt-4.5-preview": {"display_name": "[qingyun] gpt-4.5", "connection": true, "financial": true, "conn_time": 2.247466564178467, "fin_time": 3.8808422088623047, "overall": true}, "gpt-4o": {"display_name": "[qingyun] gpt-4o", "connection": true, "financial": true, "conn_time": 3.4356939792633057, "fin_time": 2.336848497390747, "overall": true}, "gpt-3.5-turbo": {"display_name": "[qingyun] gpt-3.5-turbo", "connection": true, "financial": true, "conn_time": 2.81612491607666, "fin_time": 4.523806571960449, "overall": true}, "o3": {"display_name": "[qingyun] o3", "connection": true, "financial": true, "conn_time": 3.625478982925415, "fin_time": 9.323666095733643, "overall": true}, "o4-mini": {"display_name": "[qingyun] o4-mini", "connection": true, "financial": true, "conn_time": 3.758490800857544, "fin_time": 13.136826753616333, "overall": true}, "meta-llama/llama-4-scout": {"display_name": "[qingyun] meta-llama/llama-4-scout", "connection": true, "financial": true, "conn_time": 3.7541775703430176, "fin_time": 4.7254533767700195, "overall": true}, "meta-llama/llama-4-maverick": {"display_name": "[qingyun] meta-llama/llama-4-maverick", "connection": true, "financial": true, "conn_time": 3.905538320541382, "fin_time": 3.8508481979370117, "overall": true}, "grok-beta": {"display_name": "[q<PERSON><PERSON>] grok-3-mini (placeholder)", "connection": true, "financial": true, "conn_time": 5.627992630004883, "fin_time": 11.387343645095825, "overall": true}, "grok-3-reasoner": {"display_name": "[q<PERSON><PERSON>] grok-3-reasoner", "connection": true, "financial": true, "conn_time": 10.983516931533813, "fin_time": 20.303367137908936, "overall": true}}