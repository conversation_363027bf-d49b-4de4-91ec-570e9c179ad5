# QingYun API 模型测试说明

## 概述

本文档说明如何测试回测系统中qinyun API提供的所有模型。

## 当前支持的模型

根据 `src/llm/api_models.json` 配置，qinyun API 目前支持以下模型：

### Claude 模型
- `claude-3-5-sonnet-latest` - Claude 3.5 Sonnet 最新版
- `claude-3-7-sonnet-latest` - Claude 3.7 Sonnet 最新版

### Gemini 模型  
- `gemini-2.0-flash` - Gemini 2.0 Flash
- `gemini-2.5-pro-exp-03-25` - Gemini 2.5 Pro 实验版

### GPT 模型
- `gpt-4.5-preview` - GPT-4.5 预览版
- `gpt-4o` - GPT-4o
- `gpt-3.5-turbo` - GPT-3.5 Turbo
- `o3` - OpenAI o3
- `o4-mini` - OpenAI o4 Mini

### Llama 模型
- `meta-llama/llama-4-scout` - Llama 4 Scout
- `meta-llama/llama-4-maverick` - Llama 4 Maverick

### Grok 模型
- `grok-beta` - <PERSON>rok Beta
- `grok-3-reasoner` - Grok 3 Reasoner

## 测试脚本

### 1. 综合测试脚本 (推荐)

```bash
# 快速测试（测试3个代表性模型）
python test_qingyun_comprehensive.py --quick

# 完整测试（测试所有模型）
python test_qingyun_comprehensive.py

# 保存测试结果到文件
python test_qingyun_comprehensive.py --output qingyun_test_results.json
```

### 2. 原有测试脚本

```bash
# 快速测试模式
python test_api/test_qingyun_models.py --quick

# 测试单个模型
python test_api/test_qingyun_models.py --model "meta-llama/llama-4-scout"

# 完整测试
python test_api/test_qingyun_models.py
```

### 3. 回测集成测试

```bash
# 测试模型在回测系统中的集成
python test_api/test_qingyun_backtest.py
```

## 环境配置

### 1. 设置API密钥

在 `.env` 文件中添加：

```env
QINGYUN_API_KEY=your_qingyun_api_key_here
```

### 2. 验证配置

```bash
python -c "import os; from dotenv import load_dotenv; load_dotenv(); print('API Key:', os.getenv('QINGYUN_API_KEY')[:10] + '...' if os.getenv('QINGYUN_API_KEY') else 'Not found')"
```

## 测试内容

每个测试脚本会验证：

1. **连接测试** - 验证模型是否可以正常初始化和响应
2. **金融任务测试** - 测试模型在金融分析场景下的表现
3. **响应时间** - 记录模型的响应速度
4. **错误处理** - 捕获和报告各种错误情况

## 测试结果解读

### 成功标识
- ✅ 表示测试通过
- ❌ 表示测试失败
- ❓ 表示未测试

### 性能指标
- **连接时间** - 模型初始化和基本响应时间
- **金融任务时间** - 复杂金融分析任务响应时间
- **总耗时** - 连接时间 + 金融任务时间

## 在回测系统中使用

测试通过的模型可以在回测系统中使用：

```bash
# 启动回测
python src/backtester.py --tickers NVDA --start-date 2024-01-02 --end-date 2024-12-31

# 在模型选择界面选择qinyun模型，例如：
# [qingyun] meta-llama/llama-4-scout
# [qingyun] grok-beta
# [qingyun] claude-3.5-sonnet
```

## 故障排除

### 常见问题

1. **API密钥错误**
   ```
   ❌ QINGYUN_API_KEY环境变量未设置
   ```
   **解决方案**: 检查 `.env` 文件中的API密钥配置

2. **网络连接问题**
   ```
   Connection timeout
   ```
   **解决方案**: 检查网络连接，确保可以访问 `https://api.qingyuntop.top/v1`

3. **模型不可用**
   ```
   Model not found
   ```
   **解决方案**: 确认qinyun支持所选模型，检查模型名称

4. **API限制**
   ```
   Rate limit exceeded
   ```
   **解决方案**: 等待后重试，或检查API配额

### 调试步骤

1. **验证网络连接**:
   ```bash
   curl -I https://api.qingyuntop.top/v1/models
   ```

2. **测试单个模型**:
   ```bash
   python test_qingyun_comprehensive.py --quick
   ```

3. **查看详细错误**:
   检查测试脚本输出的详细错误信息

## 模型推荐

根据测试结果，推荐使用顺序：

1. **快速响应**: `meta-llama/llama-4-maverick`
2. **复杂分析**: `meta-llama/llama-4-scout`  
3. **成本控制**: `grok-beta`
4. **高质量分析**: `claude-3.5-sonnet-latest`
5. **平衡选择**: `gpt-4o`

## 注意事项

1. 所有qinyun模型都使用 `https://api.qingyuntop.top/v1` 端点
2. 支持确定性配置 (`temperature=0.0`, `seed=42`)
3. 默认超时时间为120秒
4. 建议在测试时添加适当延迟避免API限制
5. 某些模型可能需要特殊的token限制配置
