#!/usr/bin/env python3
"""
QingYun API 模型综合测试脚本
测试回测系统中所有qinyun API提供的模型
"""

import os
import sys
import time
import json
from pathlib import Path
from dotenv import load_dotenv
from typing import Dict, List, Tuple

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider

class QingYunComprehensiveTest:
    """QingYun API 模型综合测试器"""
    
    def __init__(self):
        """初始化测试器"""
        load_dotenv()
        
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("❌ QINGYUN_API_KEY环境变量未设置")
        
        # 从api_models.json加载所有qinyun模型
        self.qingyun_models = self._load_qingyun_models()
        
        print(f"✅ QingYun API密钥已配置")
        print(f"📋 发现 {len(self.qingyun_models)} 个QingYun模型")
    
    def _load_qingyun_models(self) -> List[Dict]:
        """从api_models.json加载QingYun模型"""
        api_models_path = project_root / "src" / "llm" / "api_models.json"
        
        try:
            with open(api_models_path, 'r', encoding='utf-8') as f:
                all_models = json.load(f)
            
            # 筛选QingYun模型
            qingyun_models = [
                model for model in all_models 
                if model.get("provider") == "QingYun" and model.get("model_name") != "-"
            ]
            
            return qingyun_models
            
        except Exception as e:
            print(f"❌ 加载模型配置失败: {e}")
            return []
    
    def test_model_connection(self, model_name: str) -> Tuple[bool, str, float]:
        """测试单个模型的连接和基本响应"""
        try:
            start_time = time.time()
            
            # 初始化模型
            llm = get_model(model_name, ModelProvider.QINGYUN)
            
            # 简单测试
            response = llm.invoke("请回答：1+1=?")
            
            end_time = time.time()
            response_time = end_time - start_time
            
            # 获取响应内容
            content = response.content if hasattr(response, 'content') else str(response)
            
            return True, content[:100], response_time
            
        except Exception as e:
            return False, str(e), 0.0
    
    def test_model_financial_task(self, model_name: str) -> Tuple[bool, str, float]:
        """测试模型的金融分析任务"""
        try:
            start_time = time.time()
            
            llm = get_model(model_name, ModelProvider.QINGYUN)
            
            prompt = """作为金融分析师，请简要分析AAPL股票：
1. 当前市场表现
2. 投资建议
请保持回答简洁（不超过100字）。"""
            
            response = llm.invoke(prompt)
            
            end_time = time.time()
            response_time = end_time - start_time
            
            content = response.content if hasattr(response, 'content') else str(response)
            
            return True, content[:200], response_time
            
        except Exception as e:
            return False, str(e), 0.0
    
    def run_comprehensive_test(self, quick_mode: bool = False) -> Dict:
        """运行综合测试"""
        print(f"\n🚀 开始QingYun API模型综合测试")
        print("=" * 70)
        
        # 选择测试模型
        if quick_mode:
            # 快速模式：只测试几个代表性模型
            test_models = [
                model for model in self.qingyun_models 
                if any(key in model["model_name"] for key in ["llama-4-scout", "grok-beta", "gpt-4o"])
            ][:3]
            print(f"🏃 快速测试模式：测试 {len(test_models)} 个代表性模型")
        else:
            test_models = self.qingyun_models
            print(f"🔍 完整测试模式：测试所有 {len(test_models)} 个模型")
        
        results = {}
        
        for i, model_info in enumerate(test_models, 1):
            model_name = model_info["model_name"]
            display_name = model_info["display_name"]
            
            print(f"\n[{i}/{len(test_models)}] 测试: {display_name}")
            print("-" * 50)
            
            # 连接测试
            conn_success, conn_result, conn_time = self.test_model_connection(model_name)
            
            if conn_success:
                print(f"✅ 连接测试通过 ({conn_time:.2f}s)")
                print(f"   响应: {conn_result}...")
                
                # 金融任务测试
                fin_success, fin_result, fin_time = self.test_model_financial_task(model_name)
                
                if fin_success:
                    print(f"✅ 金融任务测试通过 ({fin_time:.2f}s)")
                    print(f"   分析: {fin_result}...")
                else:
                    print(f"❌ 金融任务测试失败: {fin_result}")
            else:
                print(f"❌ 连接测试失败: {conn_result}")
                fin_success, fin_time = False, 0.0
            
            results[model_name] = {
                'display_name': display_name,
                'connection': conn_success,
                'financial': fin_success,
                'conn_time': conn_time,
                'fin_time': fin_time,
                'overall': conn_success and fin_success
            }
            
            # API限制延迟
            if i < len(test_models):
                time.sleep(1)
        
        return results
    
    def print_summary(self, results: Dict):
        """打印测试结果摘要"""
        print(f"\n{'='*70}")
        print("📊 QingYun API 模型测试结果摘要")
        print("=" * 70)
        
        # 按类别分组
        categories = {
            "Claude模型": [],
            "Gemini模型": [],
            "GPT模型": [],
            "Llama模型": [],
            "Grok模型": [],
            "其他模型": []
        }
        
        for model_name, result in results.items():
            if "claude" in model_name.lower():
                categories["Claude模型"].append((model_name, result))
            elif "gemini" in model_name.lower():
                categories["Gemini模型"].append((model_name, result))
            elif any(x in model_name.lower() for x in ["gpt", "o3", "o4"]):
                categories["GPT模型"].append((model_name, result))
            elif "llama" in model_name.lower():
                categories["Llama模型"].append((model_name, result))
            elif "grok" in model_name.lower():
                categories["Grok模型"].append((model_name, result))
            else:
                categories["其他模型"].append((model_name, result))
        
        # 打印各类别结果
        total_tested = len(results)
        total_success = sum(1 for r in results.values() if r['overall'])
        
        for category, models in categories.items():
            if models:
                print(f"\n{category}:")
                for model_name, result in models:
                    status = "✅" if result['overall'] else "❌"
                    conn_status = "✅" if result['connection'] else "❌"
                    fin_status = "✅" if result['financial'] else "❌"
                    
                    print(f"  {status} {result['display_name']}")
                    print(f"     连接: {conn_status} ({result['conn_time']:.2f}s) | "
                          f"金融: {fin_status} ({result['fin_time']:.2f}s)")
        
        # 总体统计
        print(f"\n📈 总体统计:")
        print(f"   测试模型: {total_tested} 个")
        print(f"   成功模型: {total_success} 个")
        print(f"   成功率: {total_success/total_tested*100:.1f}%")
        
        # 推荐使用的模型
        successful_models = [
            (name, result) for name, result in results.items() 
            if result['overall']
        ]
        
        if successful_models:
            print(f"\n🎯 推荐使用的模型:")
            # 按响应时间排序
            successful_models.sort(key=lambda x: x[1]['conn_time'] + x[1]['fin_time'])
            
            for model_name, result in successful_models[:5]:  # 显示前5个最快的
                total_time = result['conn_time'] + result['fin_time']
                print(f"   ⭐ {result['display_name']} (总耗时: {total_time:.2f}s)")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="QingYun API 模型综合测试")
    parser.add_argument("--quick", action="store_true", help="快速测试模式")
    parser.add_argument("--output", type=str, help="保存结果到JSON文件")
    args = parser.parse_args()
    
    try:
        tester = QingYunComprehensiveTest()
        results = tester.run_comprehensive_test(quick_mode=args.quick)
        tester.print_summary(results)
        
        # 保存结果
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            print(f"\n💾 测试结果已保存到: {args.output}")
        
        print(f"\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        print("\n请检查:")
        print("1. QINGYUN_API_KEY 环境变量设置")
        print("2. 网络连接状态")
        print("3. QingYun API 服务状态")


if __name__ == "__main__":
    main()
