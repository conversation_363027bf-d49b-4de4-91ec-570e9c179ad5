#!/usr/bin/env python3
"""
测试QingYun API新增模型集成
验证通过QingYun API调用meta-llama/llama-4-scout、meta-llama/llama-4-maverick和grok-3-mini-beta模型
"""

import os
import sys
import time
from pathlib import Path
from dotenv import load_dotenv
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.messages import HumanMessage

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.append(str(project_root))

from src.llm.models import get_model, ModelProvider

class QingYunModelsTest:
    """QingYun新模型测试器"""

    def __init__(self):
        """初始化测试器"""
        # 加载环境变量
        load_dotenv()
        
        self.api_key = os.getenv("QINGYUN_API_KEY")
        if not self.api_key:
            raise ValueError("QINGYUN_API_KEY环境变量未设置。请在.env文件中设置您的QingYun API密钥。")

        # 要测试的模型列表 - 包含所有qinyun API支持的模型
        self.test_models = [
            # Claude模型
            "claude-3-5-sonnet-latest",
            "claude-3-7-sonnet-latest",
            # Gemini模型
            "gemini-2.0-flash",
            "gemini-2.5-pro-exp-03-25",
            # GPT模型
            "gpt-4.5-preview",
            "gpt-4o",
            "gpt-3.5-turbo",
            "o3",
            "o4-mini",
            # Llama模型
            "meta-llama/llama-4-scout",
            "meta-llama/llama-4-maverick",
            # Grok模型
            "grok-beta",
            "grok-3-reasoner"
        ]

        # 快速测试模型列表（用于快速验证）
        self.quick_test_models = [
            "meta-llama/llama-4-scout",
            "meta-llama/llama-4-maverick",
            "grok-beta"
        ]
        
        print(f"✅ QingYun API密钥已配置")
        print(f"📋 将测试以下模型: {', '.join(self.test_models)}")

    def get_financial_analysis_prompt(self, ticker: str = "AAPL") -> ChatPromptTemplate:
        """
        获取金融分析提示词，模拟对冲基金代理的实际使用场景
        
        Args:
            ticker: 股票代码
        """
        template = """你是一位专业的金融分析师。请对{ticker}股票进行简要分析，包括：
1. 当前市场表现
2. 基本面分析要点
3. 投资建议（买入/持有/卖出）

请保持分析简洁明了，不超过150字。"""

        return ChatPromptTemplate.from_template(template)

    def test_model_basic_response(self, model_name: str):
        """测试单个模型的基本响应功能"""
        print(f"\n🧪 测试模型: {model_name}")

        try:
            # 初始化模型
            llm = get_model(model_name, ModelProvider.QINGYUN)
            print(f"✅ 模型 {model_name} 初始化成功")

            # 简单的测试消息
            messages = [HumanMessage(content="请用一句话介绍你自己。")]

            start_time = time.time()
            response = llm.invoke(messages)
            end_time = time.time()

            response_time = end_time - start_time

            # 处理响应内容
            content = response.content if hasattr(response, 'content') else str(response)

            print(f"✅ 基本响应测试成功 (耗时: {response_time:.2f}秒)")
            print(f"📝 响应内容: {content[:100]}...")
            return True

        except Exception as e:
            print(f"❌ 模型 {model_name} 测试失败: {e}")
            return False

    def test_model_financial_analysis(self, model_name: str):
        """测试单个模型的金融分析场景"""
        print(f"\n📊 测试模型 {model_name} 的金融分析能力...")

        try:
            # 初始化模型
            llm = get_model(model_name, ModelProvider.QINGYUN)

            # 使用金融分析提示词
            prompt = self.get_financial_analysis_prompt("NVDA")
            messages = prompt.format_messages(ticker="NVDA")

            start_time = time.time()
            response = llm.invoke(messages)
            end_time = time.time()

            response_time = end_time - start_time

            # 处理响应内容
            content = response.content if hasattr(response, 'content') else str(response)

            print(f"✅ 金融分析测试成功 (耗时: {response_time:.2f}秒)")
            print(f"📈 NVDA分析结果:")
            print(f"{content}")
            return True

        except Exception as e:
            print(f"❌ 模型 {model_name} 金融分析测试失败: {e}")
            return False

    def test_all_models(self, quick_mode=False):
        """测试所有新增模型"""
        models_to_test = self.quick_test_models if quick_mode else self.test_models
        test_type = "快速测试" if quick_mode else "完整测试"

        print(f"\n🚀 开始{test_type}QingYun API模型...")
        print("=" * 60)
        print(f"📋 将测试 {len(models_to_test)} 个模型")

        results = {}

        for i, model_name in enumerate(models_to_test, 1):
            print(f"\n{'='*15} [{i}/{len(models_to_test)}] {model_name} {'='*15}")

            # 基本响应测试
            basic_success = self.test_model_basic_response(model_name)

            # 金融分析测试
            financial_success = False
            if basic_success:
                financial_success = self.test_model_financial_analysis(model_name)

            results[model_name] = {
                'basic': basic_success,
                'financial': financial_success,
                'overall': basic_success and financial_success
            }

            # 添加延迟避免API限制
            time.sleep(2)

        return results

    def print_summary(self, results):
        """打印测试结果摘要"""
        print(f"\n{'='*60}")
        print("🎯 测试结果摘要")
        print("=" * 60)
        
        successful_models = []
        failed_models = []
        
        for model_name, result in results.items():
            if result['overall']:
                successful_models.append(model_name)
                print(f"✅ {model_name}: 全部测试通过")
            else:
                failed_models.append(model_name)
                basic_status = "✅" if result['basic'] else "❌"
                financial_status = "✅" if result['financial'] else "❌"
                print(f"❌ {model_name}: 基本测试{basic_status} 金融分析{financial_status}")
        
        print(f"\n📊 总体结果:")
        print(f"   成功: {len(successful_models)}/{len(self.test_models)} 个模型")
        print(f"   失败: {len(failed_models)}/{len(self.test_models)} 个模型")
        
        if len(successful_models) == len(self.test_models):
            print(f"\n🎉 所有QingYun新模型测试通过！可以在回测系统中使用。")
        elif len(successful_models) > 0:
            print(f"\n⚠️ 部分模型测试通过，可以使用成功的模型进行回测。")
            print(f"   可用模型: {', '.join(successful_models)}")
        else:
            print(f"\n❌ 所有模型测试失败，请检查API配置和网络连接。")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="测试QingYun API模型")
    parser.add_argument("--quick", action="store_true", help="快速测试模式（仅测试3个核心模型）")
    parser.add_argument("--model", type=str, help="测试单个指定模型")
    args = parser.parse_args()

    try:
        # 创建测试器
        tester = QingYunModelsTest()

        if args.model:
            # 测试单个模型
            print(f"\n🎯 测试单个模型: {args.model}")
            print("=" * 60)

            basic_success = tester.test_model_basic_response(args.model)
            financial_success = False
            if basic_success:
                financial_success = tester.test_model_financial_analysis(args.model)

            results = {args.model: {
                'basic': basic_success,
                'financial': financial_success,
                'overall': basic_success and financial_success
            }}
        else:
            # 运行批量测试
            results = tester.test_all_models(quick_mode=args.quick)

        # 打印摘要
        tester.print_summary(results)

        # 显示可用模型列表
        print(f"\n📋 QingYun API支持的所有模型:")
        print("=" * 60)
        model_categories = {
            "Claude模型": ["claude-3-5-sonnet-latest", "claude-3-7-sonnet-latest"],
            "Gemini模型": ["gemini-2.0-flash", "gemini-2.5-pro-exp-03-25"],
            "GPT模型": ["gpt-4.5-preview", "gpt-4o", "gpt-3.5-turbo", "o3", "o4-mini"],
            "Llama模型": ["meta-llama/llama-4-scout", "meta-llama/llama-4-maverick"],
            "Grok模型": ["grok-beta", "grok-3-reasoner"]
        }

        for category, models in model_categories.items():
            print(f"\n{category}:")
            for model in models:
                status = "✅" if model in results and results[model]['overall'] else "❓"
                print(f"  {status} {model}")

    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        print("请确保:")
        print("1. QINGYUN_API_KEY已在.env文件中正确设置")
        print("2. 网络连接正常")
        print("3. QingYun API服务可用")


if __name__ == "__main__":
    main()
